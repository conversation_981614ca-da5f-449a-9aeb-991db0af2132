import { defineStore } from 'pinia'
import { ref } from 'vue'
import type { Theme } from '@/types'

export const useAppStore = defineStore('app', () => {
  const theme = ref<Theme>('light')
  const loading = ref(false)
  const language = ref('en')

  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
  }

  const setLoading = (isLoading: boolean) => {
    loading.value = isLoading
  }

  const setLanguage = (lang: string) => {
    language.value = lang
  }

  return {
    theme,
    loading,
    language,
    setTheme,
    setLoading,
    setLanguage
  }
})
