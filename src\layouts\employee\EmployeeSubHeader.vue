<template>
  <div class="flex flex-wrap gap-5 justify-between px-3.5 py-2 w-full text-sm leading-none text-white border-t-0 border-gray-100 bg-sky-950 max-w-[1333px] max-md:max-w-full">
    <div class="flex gap-3.5 my-auto">
      <!-- Category Dropdown -->
      <div class="flex gap-2.5 items-center relative">
        <span class="self-stretch my-auto">Category:</span>
        <button
          @click="toggleCategoryDropdown"
          class="flex gap-2.5 items-center hover:text-gray-200 transition-colors"
        >
          <span class="self-stretch my-auto font-medium">{{ employeeStore.selectedCategory?.name || 'Select Category' }}</span>
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/c3bd2bd08dcee77a7db33a56fd5ca50607d5a377?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 self-stretch my-auto w-3.5 aspect-[1.75] transform transition-transform duration-200"
            :class="{ 'rotate-180': showCategoryDropdown }"
            alt="Dropdown arrow"
          />
        </button>

        <!-- Category Dropdown Menu -->
        <div
          v-if="showCategoryDropdown"
          class="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[180px]"
        >
          <button
            v-for="category in employeeStore.categories"
            :key="category.id"
            @click="selectCategory(category.id)"
            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
            :class="{ 'bg-blue-50 text-blue-700': category.id === employeeStore.selectedCategoryId }"
          >
            {{ category.name }}
          </button>
        </div>
      </div>

      <div class="shrink-0 my-auto w-px h-4 border border-gray-100 border-solid"></div>

      <!-- Queue Dropdown -->
      <div class="flex gap-2.5 items-center relative">
        <span class="self-stretch my-auto">Queue:</span>
        <button
          @click="toggleQueueDropdown"
          class="flex gap-2.5 items-center hover:text-gray-200 transition-colors"
          :disabled="!employeeStore.selectedCategory"
        >
          <span class="self-stretch my-auto font-medium">{{ employeeStore.selectedQueue?.name || 'Select Queue' }}</span>
          <img
            src="https://cdn.builder.io/api/v1/image/assets/TEMP/c3bd2bd08dcee77a7db33a56fd5ca50607d5a377?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
            class="object-contain shrink-0 self-stretch my-auto w-3.5 aspect-[1.75] transform transition-transform duration-200"
            :class="{ 'rotate-180': showQueueDropdown }"
            alt="Dropdown arrow"
          />
        </button>

        <!-- Queue Dropdown Menu -->
        <div
          v-if="showQueueDropdown && employeeStore.availableQueues.length > 0"
          class="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 min-w-[160px]"
        >
          <button
            v-for="queue in employeeStore.availableQueues"
            :key="queue.id"
            @click="selectQueue(queue.id)"
            class="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 first:rounded-t-lg last:rounded-b-lg"
            :class="{ 'bg-blue-50 text-blue-700': queue.id === employeeStore.selectedQueueId }"
          >
            {{ queue.name }}
          </button>
        </div>
      </div>
    </div>

    <button
      @click="widgetsStore.toggleCollapse()"
      class="flex gap-2 items-center font-medium text-right hover:text-gray-200 transition-colors"
    >
      <img
        src="https://cdn.builder.io/api/v1/image/assets/TEMP/27a2fa07e99e9ebffd2acc1b272be960c3ffa4ad?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
        class="object-contain shrink-0 self-stretch my-auto w-6 rounded aspect-square transform transition-transform duration-300"
        :class="{ 'rotate-180': widgetsStore.isCollapsed }"
        alt="Collapse icon"
      />
      <span class="self-stretch my-auto">{{ widgetsStore.isCollapsed ? 'Expand Widgets' : 'Collapse Widgets' }}</span>
    </button>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useEmployeeStore } from '@/stores/employee'
import { useWidgetsStore } from '@/stores/widgets'

// Stores
const employeeStore = useEmployeeStore()
const widgetsStore = useWidgetsStore()

// Dropdown states
const showCategoryDropdown = ref(false)
const showQueueDropdown = ref(false)

// Dropdown methods
const toggleCategoryDropdown = () => {
  showCategoryDropdown.value = !showCategoryDropdown.value
  showQueueDropdown.value = false // Close other dropdown
}

const toggleQueueDropdown = () => {
  showQueueDropdown.value = !showQueueDropdown.value
  showCategoryDropdown.value = false // Close other dropdown
}

const selectCategory = (categoryId: string) => {
  employeeStore.setCategory(categoryId)
  showCategoryDropdown.value = false
  // Generate new random data when category changes
  employeeStore.generateRandomData()
}

const selectQueue = (queueId: string) => {
  employeeStore.setQueue(queueId)
  showQueueDropdown.value = false
  // Generate new random data when queue changes
  employeeStore.generateRandomData()
}

// Close dropdowns when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    showCategoryDropdown.value = false
    showQueueDropdown.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>
