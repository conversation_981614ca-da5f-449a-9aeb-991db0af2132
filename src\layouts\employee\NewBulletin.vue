<template>
  <div class="w-full bg-gray-50 min-h-screen">
    <!-- Header with <PERSON>ton -->
    <div class="bg-slate-800 py-3 px-6">
      <div class="flex items-center gap-3">
        <button
          @click="navigateBack"
          class="flex items-center gap-2 text-white hover:text-gray-200 transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
          </svg>
          <span class="text-lg font-medium">New Bulletin</span>
        </button>
      </div>
    </div>

    <!-- Main Content -->
    <div class="px-8 py-8">
      <h1 class="text-slate-800 text-3xl font-bold font-roboto mb-2">
        Create a new bulletin
      </h1>
      <p class="text-gray-600 text-lg font-roboto mb-8">
        Add a new bulletin to inform your users of the latest updates, changes and news.
      </p>

        <form @submit.prevent="handleSubmit" class="space-y-8">
          <!-- Title Field -->
          <div class="flex items-start gap-6">
            <label for="title" class="text-slate-800 font-medium font-roboto w-20 pt-3">
              Title:
            </label>
            <input
              id="title"
              v-model="form.title"
              type="text"
              placeholder="Enter a title for your bulletin"
              class="flex-1 px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white text-gray-900"
              required
            />
          </div>

          <!-- Content Field -->
          <div class="flex items-start gap-6">
            <label for="content" class="text-slate-800 font-medium font-roboto w-20 pt-3">
              Content:
            </label>
            <textarea
              id="content"
              v-model="form.content"
              rows="12"
              placeholder="Content goes here..."
              class="flex-1 px-4 py-3 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 resize-none bg-white text-gray-900"
              required
            ></textarea>
          </div>

          <!-- Mark as Urgent -->
          <div class="flex justify-end">
            <div class="flex items-center gap-3">
              <div class="relative">
                <input
                  id="urgent"
                  v-model="form.isUrgent"
                  type="checkbox"
                  class="sr-only"
                />
                <label
                  for="urgent"
                  class="flex items-center cursor-pointer"
                >
                  <div
                    class="w-5 h-5 border-2 border-gray-300 rounded flex items-center justify-center transition-colors"
                    :class="form.isUrgent ? 'bg-blue-600 border-blue-600' : 'bg-white'"
                  >
                    <svg
                      v-if="form.isUrgent"
                      class="w-3 h-3 text-white"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      />
                    </svg>
                  </div>
                  <span class="ml-3 text-gray-700 font-medium">Mark bulletin as Urgent</span>
                </label>
              </div>
            </div>
          </div>

          <!-- Footer and Action Buttons -->
          <div class="flex items-center justify-between pt-8">
            <!-- Footer Note -->
            <p class="text-gray-600 text-sm">
              Remember you can edit or delete this bulletin later.
            </p>

            <!-- Action Buttons -->
            <div class="flex gap-4">
              <button
                type="button"
                @click="handleDiscard"
                class="px-8 py-3 border-2 border-blue-500 text-blue-500 rounded-lg hover:bg-blue-50 transition-colors font-medium"
              >
                Discard
              </button>
              <button
                type="submit"
                class="px-8 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
              >
                Save
              </button>
            </div>
          </div>
        </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface BulletinForm {
  title: string
  content: string
  isUrgent: boolean
}

// Emits
const emit = defineEmits(['navigate-back'])

// Form data
const form = ref<BulletinForm>({
  title: '',
  content: '',
  isUrgent: false
})

// Navigation method
const navigateBack = () => {
  emit('navigate-back')
}

// Form methods
const handleSubmit = () => {
  // Validate form
  if (!form.value.title.trim() || !form.value.content.trim()) {
    alert('Please fill in all required fields.')
    return
  }

  // In a real app, this would save to API
  console.log('Saving bulletin:', form.value)
  
  // Show success message and navigate back
  alert('Bulletin created successfully!')
  emit('navigate-back')
}

const handleDiscard = () => {
  // Confirm discard if form has content
  if (form.value.title.trim() || form.value.content.trim()) {
    if (confirm('Are you sure you want to discard your changes?')) {
      emit('navigate-back')
    }
  } else {
    emit('navigate-back')
  }
}
</script>
