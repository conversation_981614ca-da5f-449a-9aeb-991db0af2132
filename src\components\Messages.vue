<!-- eslint-disable vue/multi-word-component-names -->
<template>
  <div class="relative messages-container">
    <!-- Messages Button -->
    <button
      @click="toggleMessages"
      class="flex gap-1.5 items-center self-stretch my-auto min-h-6 hover:bg-gray-50 px-2 py-1 rounded transition-colors duration-200"
    >
      <img
        src="https://cdn.builder.io/api/v1/image/assets/TEMP/35adfdf0ac4b21794088c003868092b796346bfa?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
        class="object-contain shrink-0 self-stretch my-auto w-6 aspect-square"
        alt="Messages icon"
      />
      <span class="self-stretch my-auto">
        <span style="font-weight: 700">{{ totalMessages }}</span> Messages
      </span>
    </button>

    <!-- Messages Panel -->
    <div
      v-if="showMessages"
      @click.stop
      class="absolute top-full right-0 mt-2 w-[720px] bg-white border border-slate-300 shadow-xl z-50 rounded-lg overflow-hidden"
    >
      <!-- Header with Tabs -->
      <div class="px-4 py-3 border-b border-gray-200">
        <div class="flex justify-between items-center mb-3">
          <div class="text-teal-950 text-lg font-bold font-roboto">
            {{ totalMessages }} Messages
          </div>
          <button
            @click.stop="closeMessages"
            class="text-cyan-600 text-base font-bold font-roboto hover:text-cyan-700 transition-colors"
          >
            Close
          </button>
        </div>

        <!-- Single Tab - Preview only -->
        <div class="flex border-b border-gray-200">
          <button
            class="px-4 py-2 text-sm font-medium font-roboto text-cyan-600 border-b-2 border-cyan-600"
          >
            Preview
          </button>
        </div>
      </div>

      <!-- Message Content -->
      <div class="overflow-y-auto max-h-[400px]">
        <div v-if="filteredMessages.length === 0" class="p-8 text-center">
          <div class="text-gray-500 text-sm">No messages found</div>
        </div>

        <div v-else>
          <div
            v-for="(message, index) in displayedMessages"
            :key="message.id"
            @click="selectMessage(message)"
            :class="[
              'p-4 border-b border-gray-100 cursor-pointer transition-colors',
              message.status === 'read' ? 'bg-gray-200' : 'bg-white hover:bg-gray-50'
            ]"
          >
            <!-- Message Content -->
            <div class="flex items-start gap-3">
              <!-- Order Index instead of icon -->
              <div class="w-8 h-8 bg-cyan-100 rounded-lg flex items-center justify-center flex-shrink-0 mt-1">
                <span class="text-cyan-600 text-sm font-bold font-roboto">{{ index + 1 }}</span>
              </div>

              <!-- Message Details -->
              <div class="flex-1 min-w-0">
                <div class="mb-2">
                  <h4 :class="[
                    'text-teal-950 text-sm font-roboto mb-1',
                    selectedMessageId === message.id ? 'font-bold' : 'font-normal'
                  ]">{{ message.title }}</h4>
                  <p :class="[
                    'text-gray-700 text-sm font-roboto',
                    selectedMessageId === message.id ? 'font-bold' : 'font-normal'
                  ]">{{ message.content }}</p>
                </div>

                <div class="flex items-center justify-between">
                  <div :class="[
                    'text-gray-500 text-xs font-roboto',
                    selectedMessageId === message.id ? 'font-bold' : 'font-normal'
                  ]">
                    {{ formatDateTime(message.createdAt) }}
                  </div>
                  <img src="@/assets/rectangle_stroke_right.svg" alt="Arrow" class="w-4 h-4" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Show More Button -->
      <div v-if="filteredMessages.length > visibleItemsCount" class="p-4 border-t border-slate-200 bg-gray-50">
        <button
          @click.stop="showMoreMessages"
          class="w-full py-2 text-sm text-cyan-600 font-medium hover:text-cyan-700 transition-colors"
        >
          Show More ({{ Math.min(itemsPerPage, filteredMessages.length - visibleItemsCount) }} more messages)
        </button>
      </div>
    </div>

    <!-- Overlay to close messages -->
    <div
      v-if="showMessages"
      @click="closeMessages"
      class="fixed inset-0 z-40"
    ></div>

    <!-- Notification Modal -->
    <NotificationModal
      :is-visible="showNotificationModal"
      :notification="selectedNotification"
      :current-index="currentNotificationIndex"
      :total-count="mockMessages.length"
      :unread-count="totalMessages"
      @close="closeNotificationModal"
      @next="handleNextNotification"
      @previous="handlePreviousNotification"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import NotificationModal from './NotificationModal.vue'

interface Message {
  id: string
  orderNumber: string
  title: string
  content: string
  createdAt: Date
  priority: 'high' | 'medium' | 'low'
  status: 'unread' | 'read'
  sender: string
  category: string
  type: 'COMMON_NOTIFICATION' | 'ACTIVITY_NOTIFICATION' | 'WARNING_NOTIFICATION'
  details?: string[]
  clientName?: string
  loanNumbers?: string[]
  actionRequired?: boolean
}

// State
const showMessages = ref(false)
const showNotificationModal = ref(false)
const selectedNotification = ref<Message | null>(null)
const selectedMessageId = ref<string | null>(null)
const currentNotificationIndex = ref(0)
const visibleItemsCount = ref(10)
const itemsPerPage = 10

// Mock data - 20 message records with different notification types
const mockMessages = ref<Message[]>([
  {
    id: '1',
    orderNumber: 'ORD-2024-001',
    title: 'This client does not have unique info',
    content: 'Customer information validation failed due to duplicate entries.',
    createdAt: new Date('2024-01-15T10:30:00'),
    priority: 'high',
    status: 'unread',
    sender: 'Validation System',
    category: 'Validation',
    type: 'COMMON_NOTIFICATION',
    details: [
      'Customer bank account number is not unique',
      'Customer address and address 2 is not unique',
      'Customer cell phone number is not unique'
    ]
  },
  {
    id: '2',
    orderNumber: 'ORD-2024-002',
    title: 'Assign Client',
    content: 'Current client, Brianna Hill, is not assigned yet. Would you like to assign this client for your login?',
    createdAt: new Date('2024-01-14T14:20:00'),
    priority: 'medium',
    status: 'unread',
    sender: 'Assignment System',
    category: 'Assignment',
    type: 'ACTIVITY_NOTIFICATION',
    clientName: 'Brianna Hill'
  },
  {
    id: '3',
    orderNumber: 'ORD-2024-003',
    title: 'Warning',
    content: 'New data received for Client. See notes for the following loan(s):',
    createdAt: new Date('2024-01-13T09:15:00'),
    priority: 'high',
    status: 'unread',
    sender: 'Data System',
    category: 'Warning',
    type: 'WARNING_NOTIFICATION',
    loanNumbers: ['********', '********', '********'],
    details: ['Denied', 'Pending Application', 'Pending Application'],
    actionRequired: true
  },
  { id: '4', orderNumber: 'ORD-2024-004', title: 'This client does not have unique info', content: 'Customer information validation failed due to duplicate entries.', createdAt: new Date('2024-01-12T16:45:00'), priority: 'medium', status: 'unread', sender: 'Verification Team', category: 'Verification', type: 'COMMON_NOTIFICATION', details: ['Customer bank account number is not unique', 'Customer address and address 2 is not unique'] },
  { id: '5', orderNumber: 'ORD-2024-005', title: 'Assign Client', content: 'Current client, John Smith, is not assigned yet. Would you like to assign this client for your login?', createdAt: new Date('2024-01-11T11:30:00'), priority: 'low', status: 'unread', sender: 'QA Team', category: 'Quality', type: 'ACTIVITY_NOTIFICATION', clientName: 'John Smith' },
  { id: '6', orderNumber: 'ORD-2024-006', title: 'Warning', content: 'New data received for Client. See notes for the following loan(s):', createdAt: new Date('2024-01-10T13:20:00'), priority: 'medium', status: 'unread', sender: 'Support Team', category: 'Support', type: 'WARNING_NOTIFICATION', loanNumbers: ['********', '********'], details: ['Approved', 'Denied'], actionRequired: true },
  { id: '7', orderNumber: 'ORD-2024-007', title: 'This client does not have unique info', content: 'Customer information validation failed due to duplicate entries.', createdAt: new Date('2024-01-09T08:45:00'), priority: 'low', status: 'unread', sender: 'Inventory Mgmt', category: 'Inventory', type: 'COMMON_NOTIFICATION', details: ['Customer cell phone number is not unique', 'Customer email address is not unique'] },
  { id: '8', orderNumber: 'ORD-2024-008', title: 'Assign Client', content: 'Current client, Sarah Johnson, is not assigned yet. Would you like to assign this client for your login?', createdAt: new Date('2024-01-08T15:10:00'), priority: 'high', status: 'unread', sender: 'Address Verification', category: 'Verification', type: 'ACTIVITY_NOTIFICATION', clientName: 'Sarah Johnson' },
  { id: '9', orderNumber: 'ORD-2024-009', title: 'Warning', content: 'New data received for Client. See notes for the following loan(s):', createdAt: new Date('2024-01-07T12:30:00'), priority: 'medium', status: 'unread', sender: 'Delivery Service', category: 'Delivery', type: 'WARNING_NOTIFICATION', loanNumbers: ['00024259', '00024260', '00024261'], details: ['Pending Application', 'Approved', 'Denied'], actionRequired: true },
  { id: '10', orderNumber: 'ORD-2024-010', title: 'This client does not have unique info', content: 'Customer information validation failed due to duplicate entries.', createdAt: new Date('2024-01-06T10:15:00'), priority: 'medium', status: 'unread', sender: 'Returns Dept', category: 'Returns', type: 'COMMON_NOTIFICATION', details: ['Customer SSN is not unique', 'Customer address and address 2 is not unique'] },
  { id: '11', orderNumber: 'ORD-2024-011', title: 'Assign Client', content: 'Current client, Michael Brown, is not assigned yet. Would you like to assign this client for your login?', createdAt: new Date('2024-01-05T14:40:00'), priority: 'high', status: 'unread', sender: 'Warehouse', category: 'Handling', type: 'ACTIVITY_NOTIFICATION', clientName: 'Michael Brown' },
  { id: '12', orderNumber: 'ORD-2024-012', title: 'Warning', content: 'New data received for Client. See notes for the following loan(s):', createdAt: new Date('2024-01-04T09:25:00'), priority: 'low', status: 'unread', sender: 'Promotions Team', category: 'Promotions', type: 'WARNING_NOTIFICATION', loanNumbers: ['********'], details: ['Pending Application'], actionRequired: true },
  { id: '13', orderNumber: 'ORD-2024-013', title: 'This client does not have unique info', content: 'Customer information validation failed due to duplicate entries.', createdAt: new Date('2024-01-03T16:20:00'), priority: 'medium', status: 'unread', sender: 'Credit Dept', category: 'Credit', type: 'COMMON_NOTIFICATION', details: ['Customer bank account number is not unique', 'Customer cell phone number is not unique'] },
  { id: '14', orderNumber: 'ORD-2024-014', title: 'Assign Client', content: 'Current client, Emily Davis, is not assigned yet. Would you like to assign this client for your login?', createdAt: new Date('2024-01-02T11:50:00'), priority: 'low', status: 'unread', sender: 'Bulk Processing', category: 'Processing', type: 'ACTIVITY_NOTIFICATION', clientName: 'Emily Davis' },
  { id: '15', orderNumber: 'ORD-2024-015', title: 'Warning', content: 'New data received for Client. See notes for the following loan(s):', createdAt: new Date('2024-01-01T13:30:00'), priority: 'low', status: 'unread', sender: 'Feedback Team', category: 'Feedback', type: 'WARNING_NOTIFICATION', loanNumbers: ['********', '********'], details: ['Approved', 'Denied'], actionRequired: true },
  { id: '16', orderNumber: 'ORD-2024-016', title: 'This client does not have unique info', content: 'Customer information validation failed due to duplicate entries.', createdAt: new Date('2023-12-31T15:45:00'), priority: 'medium', status: 'unread', sender: 'Shipping Upgrade', category: 'Shipping', type: 'COMMON_NOTIFICATION', details: ['Customer email address is not unique', 'Customer address and address 2 is not unique'] },
  { id: '17', orderNumber: 'ORD-2024-017', title: 'Assign Client', content: 'Current client, David Wilson, is not assigned yet. Would you like to assign this client for your login?', createdAt: new Date('2023-12-30T08:20:00'), priority: 'low', status: 'unread', sender: 'IT Department', category: 'System', type: 'ACTIVITY_NOTIFICATION', clientName: 'David Wilson' },
  { id: '18', orderNumber: 'ORD-2024-018', title: 'Warning', content: 'New data received for Client. See notes for the following loan(s):', createdAt: new Date('2023-12-29T12:10:00'), priority: 'high', status: 'unread', sender: 'Compliance Team', category: 'Compliance', type: 'WARNING_NOTIFICATION', loanNumbers: ['00024265', '00024266', '00024267'], details: ['Pending Application', 'Approved', 'Denied'], actionRequired: true },
  { id: '19', orderNumber: 'ORD-2024-019', title: 'This client does not have unique info', content: 'Customer information validation failed due to duplicate entries.', createdAt: new Date('2023-12-28T14:35:00'), priority: 'medium', status: 'unread', sender: 'International Shipping', category: 'Shipping', type: 'COMMON_NOTIFICATION', details: ['Customer SSN is not unique', 'Customer cell phone number is not unique'] },
  { id: '20', orderNumber: 'ORD-2024-020', title: 'Assign Client', content: 'Current client, Lisa Anderson, is not assigned yet. Would you like to assign this client for your login?', createdAt: new Date('2023-12-27T16:55:00'), priority: 'low', status: 'unread', sender: 'Order Management', category: 'Completion', type: 'ACTIVITY_NOTIFICATION', clientName: 'Lisa Anderson' }
])



// Computed properties
const filteredMessages = computed(() => {
  return [...mockMessages.value].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
})

const displayedMessages = computed(() => {
  return filteredMessages.value.slice(0, visibleItemsCount.value)
})

const totalMessages = computed(() => {
  return mockMessages.value.filter(msg => msg.status === 'unread').length
})

// Methods
const toggleMessages = () => {
  showMessages.value = !showMessages.value
  if (showMessages.value) {
    visibleItemsCount.value = itemsPerPage
  }
}

const closeMessages = () => {
  showMessages.value = false
  selectedMessageId.value = null
}

const showMoreMessages = () => {
  visibleItemsCount.value += itemsPerPage
}

const selectMessage = async (message: Message) => {
  // Set selected message ID for bold styling
  selectedMessageId.value = message.id

  // Find the current index in the full messages array
  currentNotificationIndex.value = mockMessages.value.findIndex(m => m.id === message.id)

  // Mark as read and store in localStorage
  if (message.status === 'unread') {
    // Find the message in the array and update it properly for reactivity
    const messageIndex = mockMessages.value.findIndex(m => m.id === message.id)
    if (messageIndex !== -1) {
      // Create a new object to ensure reactivity
      mockMessages.value[messageIndex] = { ...mockMessages.value[messageIndex], status: 'read' }
    }
    updateReadStatus(message.id, true)

    // Wait for DOM update to ensure background color changes
    await nextTick()
  }

  // Set the selected notification directly (use the updated message)
  const updatedMessage = mockMessages.value.find(m => m.id === message.id) || message
  selectedNotification.value = updatedMessage
  showNotificationModal.value = true
  showMessages.value = false
}

const closeNotificationModal = () => {
  showNotificationModal.value = false
  selectedNotification.value = null
  selectedMessageId.value = null
}

const handleNextNotification = () => {
  if (currentNotificationIndex.value < mockMessages.value.length - 1) {
    currentNotificationIndex.value++
    selectedNotification.value = mockMessages.value[currentNotificationIndex.value]
  }
}

const handlePreviousNotification = () => {
  if (currentNotificationIndex.value > 0) {
    currentNotificationIndex.value--
    selectedNotification.value = mockMessages.value[currentNotificationIndex.value]
  }
}

const updateReadStatus = (messageId: string, isRead: boolean) => {
  const readMessages = JSON.parse(localStorage.getItem('readMessages') || '{}')
  readMessages[messageId] = isRead
  localStorage.setItem('readMessages', JSON.stringify(readMessages))
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true
  })
}

// Close messages when clicking outside
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  const messagesContainer = target.closest('.messages-container')
  if (!messagesContainer) {
    showMessages.value = false
  }
}

// Initialize read status from localStorage
const initializeReadStatus = () => {
  const readMessages = JSON.parse(localStorage.getItem('readMessages') || '{}')

  // Update regular messages using proper array mutation
  mockMessages.value.forEach((message, index) => {
    if (readMessages[message.id]) {
      mockMessages.value[index] = { ...message, status: 'read' }
    }
  })
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  initializeReadStatus()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.font-roboto {
  font-family: 'Roboto', sans-serif;
}
</style>
