<template>
  <div class="flex bg-gray-50 min-h-screen">
    <!-- Main Content -->
    <div class="flex-1 p-6">
      <!-- Header -->
      <div class="mb-6">
        <h1 class="text-slate-800 text-2xl font-bold font-roboto mb-2">
          Customized Help Section
        </h1>
        <p class="text-gray-600 text-base font-roboto">
          Determine the FAQs that might be helpful for your users and make bulletins to keep them up to date.
        </p>
      </div>

      <!-- Divider Line -->
      <div class="border-t border-gray-200 mb-8"></div>

      <!-- Frequently Asked Questions Section -->
      <section class="mb-8">
        <h2 class="text-slate-800 text-xl font-bold font-roboto mb-2">
          Frequently Asked Questions
        </h2>
        <p class="text-gray-600 text-sm font-roboto mb-6">
          If you consider your users might have some specific doubts and questions, solve them here.
        </p>

        <div class="space-y-4 mb-6">
          <div v-for="(faq, index) in faqs" :key="index" class="bg-white rounded-lg border border-gray-200 text-gray-600">
            <div class="flex">
              <!-- Number with background -->
              <div class="flex-shrink-0 w-20 bg-gray-100 rounded-l-lg flex items-center justify-center">
                <span class="text-gray-800 font-bold text-2xl">{{ index + 1 }}</span>
              </div>

              <!-- Content -->
              <div class="flex-1 p-6 space-y-4">
                <!-- FAQ -->
                <div class="flex items-center gap-4">
                  <label class="text-sm font-medium text-gray-800 w-16">FAQ:</label>
                  <input
                    v-model="faq.question"
                    type="text"
                    placeholder="Enter a question"
                    class="flex-1 px-4 py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  />
                </div>

                <!-- Answer -->
                <div class="flex items-center gap-4">
                  <label class="text-sm font-medium text-gray-800 w-16">Answer:</label>
                  <input
                    v-model="faq.answer"
                    type="text"
                    placeholder="Your answer goes here..."
                    class="flex-1 px-4 py-3 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Add FAQ Button -->
        <div class="border-2 border-solid border-blue-400 rounded-xl p-8 text-center bg-blue-50/30">
          <button
            @click="addFAQ"
            class="inline-flex items-center justify-center w-12 h-12 bg-white border-2 border-blue-400 text-blue-500 rounded-lg hover:bg-blue-50 transition-colors shadow-sm"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
          </button>
        </div>
      </section>

      <!-- Divider Line -->
      <div class="border-t border-gray-200 mb-8"></div>

      <!-- Bulletins Section -->
      <section class="mb-8">
        <h2 class="text-slate-800 text-xl font-bold font-roboto mb-2">
          Bulletins
        </h2>
        <p class="text-gray-600 text-sm font-roboto mb-6">
          Add bulletins to inform your users of the latest updates, changes and news.
        </p>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
          <div v-for="bulletin in bulletins" :key="bulletin.id" class="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
            <!-- Content -->
            <div class="mb-4">
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-slate-800 text-lg font-bold font-roboto">{{ bulletin.title }}</h3>
                <div class="flex items-center gap-1">
                  <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span class="text-blue-500 text-sm font-medium">{{ bulletin.tag }}</span>
                </div>
              </div>
              <p class="text-gray-600 text-sm mb-3 leading-relaxed">{{ bulletin.description }}</p>
              <p class="text-gray-500 text-sm">{{ bulletin.date }}</p>
            </div>
            <!-- Actions -->
            <div class="flex gap-3 justify-end">
              <button class="w-10 h-10 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                </svg>
              </button>
              <button class="w-10 h-10 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
              </button>
            </div>
          </div>

          <!-- Add Bulletin Button - Positioned as last item in grid -->
          <div class="border-2 border-solid border-blue-400 rounded-xl bg-blue-50/30 flex items-center justify-center min-h-[200px]">
            <button
              @click="navigateToNewBulletin"
              class="inline-flex items-center justify-center w-12 h-12 bg-white border-2 border-blue-400 text-blue-500 rounded-lg hover:bg-blue-50 transition-colors shadow-sm"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
            </button>
          </div>
        </div>
      </section>

      <!-- Action Buttons -->
      <div class="flex justify-between items-center">
        <p class="text-gray-600 text-sm font-roboto">
          Once you customize this section, you can save your changes or reset at any time.
        </p>
        <div class="flex gap-3">
          <button class="px-6 py-2 border border-gray-300 text-gray-700 text-sm rounded hover:bg-gray-50 transition-colors font-roboto">
            Reset to Default
          </button>
          <button class="px-6 py-2 bg-blue-500 text-white text-sm rounded hover:bg-blue-600 transition-colors font-roboto">
            Save Changes
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

interface FAQ {
  question: string
  answer: string
}

interface Bulletin {
  id: number
  tag: string
  title: string
  description: string
  date: string
}

// FAQ data
const faqs = ref<FAQ[]>([
  { question: 'How do I reset my password?', answer: 'You can reset your password by clicking on the "Forgot Password" link on the login page.' },
  { question: 'How do I contact customer support?', answer: 'You can reach our customer support team via <NAME_EMAIL> or by phone at **************.' },
  { question: 'What are your business hours?', answer: 'Our business hours are Monday through Friday, 9:00 AM to 6:00 PM EST.' }
])

// Bulletin data
const bulletins = ref<Bulletin[]>([
  {
    id: 1,
    tag: 'Urgent',
    title: 'Important update released',
    description: 'Version 3.0.2 is out now. It consists of bug fixes and improved...',
    date: 'AUG 10, 2023'
  },
  {
    id: 2,
    tag: 'New',
    title: 'New guide available',
    description: 'Our new user guide "Advanced Tips", is now available in our si...',
    date: 'JUL 28, 2023'
  },
  {
    id: 3,
    tag: 'Update',
    title: 'Latest update is here',
    description: 'Version 3.0.1 is out now. It consists of design improvements...',
    date: 'JUL 25, 2023'
  }
])

// Methods
const addFAQ = () => {
  faqs.value.push({ question: 'Enter a question', answer: 'Your answer goes here...' })
}

const navigateToNewBulletin = () => {
  emit('navigate-to-new-bulletin')
}

// Define emits
const emit = defineEmits(['navigate-to-new-bulletin'])
</script>
