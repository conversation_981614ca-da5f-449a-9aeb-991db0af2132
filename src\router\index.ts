import { createRouter, createWebHistory } from "vue-router";
import type { RouteRecordRaw } from "vue-router";

const routes: RouteRecordRaw[] = [
  {
    path: "/",
    redirect: "/dashboard",
  },
  {
    path: "/dashboard",
    name: "Dashboard",
    component: () => import("@/layouts/dashboard/Dashboard.vue"),
  },
  {
    path: "/employee-denied-queue",
    name: "EmployeeDeniedQueue",
    component: () => import("@/layouts/dashboard/Dashboard.vue"),
  },
  {
    path: "/help",
    name: "Help",
    component: () => import("@/layouts/employee/HelpContent.vue"),
  },
  {
    path: "/help/bulletin/:id",
    name: "BulletinDetail",
    component: () => import("@/layouts/employee/BulletinDetail.vue"),
  },
  {
    path: "/help/customize",
    name: "HelpCustomize",
    component: () => import("@/layouts/employee/HelpCustomize.vue"),
  },
  {
    path: "/help/new-bulletin",
    name: "NewBulletin",
    component: () => import("@/layouts/employee/NewBulletin.vue"),
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("@/views/HomeView.vue"),
  },
  {
    path: "/about",
    name: "About",
    component: () => import("@/views/AboutView.vue"),
  },
];

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
});

export default router;
