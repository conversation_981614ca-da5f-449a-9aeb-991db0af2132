# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# Dependency directories
node_modules/
jspm_packages/

# Build outputs
dist/
dist-ssr/
build/
out/

# Coverage directory used by tools like istanbul
coverage/
*.lcov
.nyc_output

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
*.local

# Cache directories
.cache/
.parcel-cache/
.eslintcache
.stylelintcache
*.tsbuildinfo

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
*.lnk

# Testing
/cypress/videos/
/cypress/screenshots/
test-results/
playwright-report/
playwright/.cache/

# Editor directories and files
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
*.iml
*.ipr
*.iws

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Vue specific
*.vue.js
*.vue.ts
.vue-devtools

# Storybook
storybook-static

# Optional: Lock files (uncomment one if needed)
# package-lock.json  # Uncomment if using yarn exclusively
# yarn.lock         # Uncomment if using npm exclusively
