<template>
  <!-- Modal Overlay -->
  <div
    v-if="isVisible"
    class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[100]"
    @click.self="closeModal"
  >
    <!-- Modal Content -->
    <div class="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[90vh] overflow-hidden">
      <!-- <PERSON><PERSON> Header -->
      <div class="bg-gradient-to-r from-cyan-600 to-teal-600 px-6 py-4 text-white">
        <div class="flex items-center justify-between">
          <div class="flex items-center gap-3">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/35adfdf0ac4b21794088c003868092b796346bfa?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
              class="w-6 h-6 filter brightness-0 invert"
              alt="Message icon"
            />
            <h2 class="text-xl font-bold font-roboto">Message Details</h2>
          </div>
          <button
            @click="closeModal"
            class="text-white hover:text-gray-200 transition-colors p-1 rounded-full hover:bg-white hover:bg-opacity-20"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
      </div>

      <!-- Modal Body -->
      <div class="p-6 overflow-y-auto max-h-[calc(90vh-120px)]" v-if="message">
        <!-- Message Info Section -->
        <div class="bg-gray-50 rounded-xl p-4 mb-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- Order Number -->
            <div class="flex items-center gap-2">
              <span class="text-gray-600 text-sm font-medium font-roboto">Order Number:</span>
              <span class="text-teal-950 text-sm font-bold font-roboto">{{ message.orderNumber }}</span>
            </div>
            
            <!-- Created Date -->
            <div class="flex items-center gap-2">
              <span class="text-gray-600 text-sm font-medium font-roboto">Created:</span>
              <span class="text-teal-950 text-sm font-roboto">{{ formatDateTime(message.createdAt) }}</span>
            </div>
            
            <!-- Sender -->
            <div class="flex items-center gap-2">
              <span class="text-gray-600 text-sm font-medium font-roboto">From:</span>
              <span class="text-teal-950 text-sm font-roboto">{{ message.sender }}</span>
            </div>
            
            <!-- Priority -->
            <div class="flex items-center gap-2">
              <span class="text-gray-600 text-sm font-medium font-roboto">Priority:</span>
              <span 
                :class="getPriorityClass(message.priority)"
                class="px-2 py-1 rounded-full text-xs font-bold uppercase"
              >
                {{ message.priority }}
              </span>
            </div>
            
            <!-- Category -->
            <div class="flex items-center gap-2">
              <span class="text-gray-600 text-sm font-medium font-roboto">Category:</span>
              <span class="text-teal-950 text-sm font-roboto">{{ message.category }}</span>
            </div>
            
            <!-- Status -->
            <div class="flex items-center gap-2">
              <span class="text-gray-600 text-sm font-medium font-roboto">Status:</span>
              <span 
                :class="getStatusClass(message.status)"
                class="px-2 py-1 rounded-full text-xs font-bold uppercase"
              >
                {{ message.status }}
              </span>
            </div>
          </div>
        </div>

        <!-- Message Title -->
        <div class="mb-4">
          <h3 class="text-xl font-bold text-teal-950 font-roboto mb-2">{{ message.title }}</h3>
        </div>

        <!-- Message Content -->
        <div class="bg-white border border-gray-200 rounded-xl p-4 mb-6">
          <div class="text-gray-700 text-base font-roboto leading-relaxed whitespace-pre-wrap">
            {{ message.content }}
          </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex flex-col sm:flex-row gap-3 justify-end">
          <button
            @click="markAsRead"
            v-if="message.status === 'unread'"
            class="px-4 py-2 bg-cyan-600 text-white rounded-lg hover:bg-cyan-700 transition-colors font-medium font-roboto"
          >
            Mark as Read
          </button>
          
          <button
            @click="replyToMessage"
            class="px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors font-medium font-roboto"
          >
            Reply
          </button>
          
          <button
            @click="forwardMessage"
            class="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium font-roboto"
          >
            Forward
          </button>
          
          <button
            @click="archiveMessage"
            class="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium font-roboto"
          >
            Archive
          </button>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="bg-gray-50 px-6 py-4 border-t border-gray-200">
        <div class="flex justify-between items-center">
          <div class="text-sm text-gray-500 font-roboto">
            Message ID: {{ message?.id }}
          </div>
          <button
            @click="closeModal"
            class="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors font-medium font-roboto"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

interface Message {
  id: string
  orderNumber: string
  title: string
  content: string
  createdAt: Date
  priority: 'high' | 'medium' | 'low'
  status: 'unread' | 'read'
  sender: string
  category: string
}

interface Props {
  isVisible: boolean
  message: Message | null
}

interface Emits {
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Methods
const closeModal = () => {
  emit('close')
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  })
}

const getPriorityClass = (priority: string) => {
  switch (priority) {
    case 'high':
      return 'bg-red-100 text-red-800'
    case 'medium':
      return 'bg-yellow-100 text-yellow-800'
    case 'low':
      return 'bg-green-100 text-green-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const getStatusClass = (status: string) => {
  switch (status) {
    case 'unread':
      return 'bg-blue-100 text-blue-800'
    case 'read':
      return 'bg-gray-500 text-gray-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

const markAsRead = () => {
  if (props.message) {
    // eslint-disable-next-line vue/no-mutating-props
    props.message.status = 'read'
  }
}

const replyToMessage = () => {
  // Implement reply functionality
  console.log('Reply to message:', props.message?.id)
  // You can add a toast notification or open a reply modal here
  alert('Reply functionality would be implemented here')
}

const forwardMessage = () => {
  // Implement forward functionality
  console.log('Forward message:', props.message?.id)
  alert('Forward functionality would be implemented here')
}

const archiveMessage = () => {
  // Implement archive functionality
  console.log('Archive message:', props.message?.id)
  alert('Message archived successfully')
  closeModal()
}
</script>

<style scoped>
.font-roboto {
  font-family: 'Roboto', sans-serif;
}

/* Custom scrollbar for modal content */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
