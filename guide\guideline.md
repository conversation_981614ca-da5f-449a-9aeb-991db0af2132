1. In UI, when we select queue = "Loan Approval" (src\stores\employee.ts), we will have the new screen for Loan Approval
2. Create new component LoanApprovalComponent.vue in (src\layouts\employee\queue), then replace "          <section class="w-full">
            <DeniedReasonsChart />
          </section>"

in src\layouts\employee\DeniedQueueContent.vue (replace is appear when User select queue = "Loan Approval", other option will redirect to "<DeniedReasonsChart />")
3. UI for LoanApprovalComponent should follow some criteria as below:
3.0: The size, color, border, position similar to <DeniedReasonsChart />
3.1. Follow guide\image.png " I should see a list of ACH providers sorted by name or default order
And each provider should display:
Provider Name
Number of Loans
Percentage of Total"

3.2 Follow: guide\image3.png " I click on the "View details" button for a provider"
Then the card should update in-place to show:
The provider name (e.g., Dharma)
A list of loan IDs associated with that provider
A summary count and percentage pill (e.g., 12 (64%))

3.3: Follow: guide\image3.png "When I click the back arrow icon
Then the card should be returned to the main summary list of providers
And all provider rows and counts should be restored to their previous state"

3.4 Follow guide\image2.png: 
"Given there are no loans eligible for approval sorted bt ACH providers today
And the total number of eligible loans should show as 0
And a message such as "No loans eligible for approval" should be displayed instead of the provider list
"