<template>
  <div class="flex overflow-hidden flex-col items-end pt-16 pb-3.5 pl-20 bg-slate-100 max-md:pl-5">
    <EmployeeHeader />
    <EmployeeSubHeader />

    <main class="mt-3.5 w-full max-w-[1304px] max-md:mr-2.5 max-md:max-w-full">
      <div class="flex gap-5 max-md:flex-col">
        <section class="w-[33%] max-md:ml-0 max-md:w-full">
          <StatsWidget
            title="Loans by Denied Reason"
            :value="17"
            :items="statsItems"
          />
        </section>

        <section class="ml-5 w-[33%] max-md:ml-0 max-md:w-full">
          <DeniedReasonsChart />
        </section>

        <section class="ml-5 w-[33%] max-md:ml-0 max-md:w-full">
          <TopReasonsWidget />
        </section>
      </div>
    </main>

    <DeniedQueueTable />
  </div>
</template>

<script setup lang="ts">
import EmployeeHeader from './EmployeeHeader.vue';
import EmployeeSubHeader from './EmployeeSubHeader.vue';
import StatsWidget from './StatsWidget.vue';
import DeniedReasonsChart from './DeniedReasonsChart.vue';
import TopReasonsWidget from './TopReasonsWidget.vue';
import DeniedQueueTable from './DeniedQueueTable.vue';

const statsItems = [
  { label: 'Invalid Documents', value: 8 },
  { label: 'Unreliable', value: 4 },
  { label: 'Bad Business Info', value: 2 },
  { label: 'Bankruptcy', value: 2 }
];
</script>
