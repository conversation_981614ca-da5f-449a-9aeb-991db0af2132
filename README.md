# Monarch UI - Vue 3 Project

A Vue 3 project using TypeScript, following a clean and modular architecture.

---

## 📁 Project Structure

```
src/
├── components/ # Reusable Vue components
├── decorators/ # TypeScript decorators
├── hooks/ # Composition API hooks
├── i18n/ # Internationalization config
│ └── locales/ # Language files (en.json, vi.json)
├── layouts/ # Layout components
├── plugins/ # Vue plugins and setup
├── router/ # Vue Router configuration
├── stores/ # Pinia stores (state management)
├── styles/ # Global styles and utilities
├── types/ # TypeScript type definitions
├── views/ # Page-level components
├── App.vue # Root component
├── components.d.ts # Auto-generated component typings
└── main.ts # Application entry point
```


## ✨ Features

- ✅ Vue 3 with Composition API  
- ✅ TypeScript  
- ✅ Vue Router 4  
- ✅ Pinia for state management  
- ✅ Vue I18n for internationalization  
- ✅ CSS Variables & Utility Classes  
- ✅ Composable Hooks  
- ✅ TypeScript Decorators  
- ✅ Modular and scalable architecture  

---

## ⚙️ Setup & Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Lint the code
npm run lint
```

📚 Usage Guide
Components
Reusable UI components are stored in src/components/.

Views (Pages)
Page-level components go in src/views/.

State Management
Pinia stores are defined in src/stores/.

Routing
Configure routes in src/router/index.ts.

Styling
CSS variables: src/styles/variables.css

Component styles: src/styles/components.css

Utility classes: src/styles/utilities.css

Internationalization
Translation files live in src/i18n/locales/.

Hooks
Reusable Composition API logic is in src/hooks/.

Types
Custom types are stored in src/types/.