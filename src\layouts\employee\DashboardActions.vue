<template>
  <nav class="flex flex-auto gap-2.5 items-center font-medium text-cyan-500">
    <img
      src="https://cdn.builder.io/api/v1/image/assets/TEMP/d400eb976c922c99bad2cd919136d3aceda18ed7?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
      class="object-contain shrink-0 self-stretch w-6 aspect-square"
      alt="Customize icon"
    />
    <button class="self-stretch my-auto hover:text-cyan-400 transition-colors">
      Customize
    </button>
    <div
      class="shrink-0 self-stretch my-auto w-px h-4 border border-gray-100 border-solid"
      role="separator"
    ></div>
    <img
      src="https://cdn.builder.io/api/v1/image/assets/TEMP/7a05d92f70cf51d839867d2fe6d49070fd3373dd?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
      class="object-contain shrink-0 self-stretch w-6 aspect-square"
      alt="Help icon"
    />
    <button class="self-stretch my-auto hover:text-cyan-400 transition-colors">
      Help
    </button>
    <div
      class="shrink-0 self-stretch my-auto w-px h-4 border border-gray-100 border-solid"
      role="separator"
    ></div>
    <img
      src="https://cdn.builder.io/api/v1/image/assets/TEMP/c58da17850dd94a2cc9da6f7712ca837ad8d3b9a?placeholderIfAbsent=true&apiKey=ffba1b847e0b48868b721cf7fcafebde"
      class="object-contain shrink-0 self-stretch w-6 aspect-square"
      alt="Notices icon"
    />
    <button class="self-stretch my-auto hover:text-cyan-400 transition-colors">
      Notices (2)
    </button>
  </nav>
</template>

<script setup lang="ts">
// No props needed for this component as it's static content
</script>
